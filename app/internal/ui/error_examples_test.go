package ui

import (
	"testing"
)

// TestErrorMessageExamples demonstrates the before/after of error message sanitization
func TestErrorMessageExamples(t *testing.T) {
	server := &Server{}

	examples := []struct {
		scenario        string
		technicalError  string
		errorType       UIErrorType
		expectedMessage string
		expectedStatus  int
	}{
		{
			scenario:        "Backend service unavailable",
			technicalError:  "failed to make request: Post \"http://localhost:8080/api/auth/login\": dial tcp [::1]:8080: connect: connection refused",
			errorType:       ErrorTypeNetwork,
			expectedMessage: "Unable to connect to our services. Please try again in a few moments.",
			expectedStatus:  503,
		},
		{
			scenario:        "Invalid credentials",
			technicalError:  "authentication failed: Incorrect email or password",
			errorType:       ErrorTypeAuthentication,
			expectedMessage: "Login failed. Please check your email and password.",
			expectedStatus:  401,
		},
		{
			scenario:        "JSON parsing error",
			technicalError:  "failed to decode response: invalid character 'x' looking for beginning of value",
			errorType:       ErrorTypeSystem,
			expectedMessage: "A system error occurred. Please try again later.",
			expectedStatus:  500,
		},
		{
			scenario:        "Network timeout",
			technicalError:  "request timed out - please check your connection and try again",
			errorType:       ErrorTypeNetwork,
			expectedMessage: "Unable to connect to our services. Please try again in a few moments.",
			expectedStatus:  503,
		},
	}

	for _, example := range examples {
		t.Run(example.scenario, func(t *testing.T) {
			// Create a mock error
			err := &testError{message: example.technicalError}

			// Test error categorization
			actualType := server.categorizeLoginError(err)
			if actualType != example.errorType {
				t.Errorf("Expected error type %s, got %s", example.errorType, actualType)
			}

			// Test error sanitization
			uiError := server.sanitizeAuthError(err, example.errorType)
			if uiError.UserMessage != example.expectedMessage {
				t.Errorf("Expected user message '%s', got '%s'", example.expectedMessage, uiError.UserMessage)
			}

			if uiError.StatusCode != example.expectedStatus {
				t.Errorf("Expected status code %d, got %d", example.expectedStatus, uiError.StatusCode)
			}

			// Verify technical details are preserved for logging
			if uiError.LogMessage != example.technicalError {
				t.Errorf("Expected log message to preserve technical details")
			}

			t.Logf("✅ %s:", example.scenario)
			t.Logf("   Technical Error: %s", example.technicalError)
			t.Logf("   User Message:    %s", uiError.UserMessage)
			t.Logf("   HTTP Status:     %d", uiError.StatusCode)
			t.Logf("   Error Type:      %s", uiError.Type)
		})
	}
}

// TestOWASPComplianceExamples demonstrates OWASP-compliant error handling
func TestOWASPComplianceExamples(t *testing.T) {
	t.Log("=== OWASP Authentication Error Handling Compliance ===")

	// Example of what NOT to do (information leakage)
	badExamples := []string{
		"Login failed, invalid user ID.",
		"Login failed; account disabled.",
		"This email address doesn't exist in our database.",
		"Connection to database server at *************:5432 failed",
	}

	// Example of what TO do (generic, secure messages)
	goodExamples := []string{
		"Login failed. Please check your email and password.",
		"Unable to connect to our services. Please try again in a few moments.",
		"A system error occurred. Please try again later.",
	}

	t.Log("\n❌ BAD Examples (Information Leakage):")
	for _, bad := range badExamples {
		t.Logf("   - %s", bad)
	}

	t.Log("\n✅ GOOD Examples (Secure & User-Friendly):")
	for _, good := range goodExamples {
		t.Logf("   - %s", good)
	}

	t.Log("\n🔒 Security Benefits:")
	t.Log("   - Prevents user enumeration attacks")
	t.Log("   - Hides internal system architecture")
	t.Log("   - Provides consistent user experience")
	t.Log("   - Maintains detailed logging for debugging")
}
