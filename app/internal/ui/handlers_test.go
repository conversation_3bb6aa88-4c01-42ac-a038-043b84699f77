package ui

import (
	"log/slog"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"
)

// TestHandleLoginPostLogging demonstrates the logging behavior of handleLoginPost
// This test shows that the handler attempts to use the logging infrastructure
// even when the context isn't properly set up (which happens in real usage via middleware)
func TestHandleLoginPostLogging(t *testing.T) {
	// Create test server
	server := &Server{
		logger:      slog.Default(),
		authService: NewAuthService("http://localhost:8080"), // This will fail, which is what we want for testing
		config:      &Config{Environment: "test"},
	}

	tests := []struct {
		name           string
		email          string
		password       string
		scenario       string
		expectedStatus int
	}{
		{
			name:           "validation error - missing email",
			email:          "",
			password:       "password123",
			scenario:       "Should log validation failure with missing_fields=email",
			expectedStatus: http.StatusBadRequest, // 400
		},
		{
			name:           "validation error - missing both",
			email:          "",
			password:       "",
			scenario:       "Should log validation failure with missing_fields=email,password",
			expectedStatus: http.StatusBadRequest, // 400
		},
		{
			name:           "network error - backend unavailable",
			email:          "<EMAIL>",
			password:       "password123",
			scenario:       "Should log network error with email_domain=example.com",
			expectedStatus: http.StatusServiceUnavailable, // 503
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Logf("Testing scenario: %s", tt.scenario)

			// Create request
			form := url.Values{}
			form.Add("email", tt.email)
			form.Add("password", tt.password)

			req := httptest.NewRequest("POST", "/login", strings.NewReader(form.Encode()))
			req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

			// Create response recorder
			w := httptest.NewRecorder()

			// Call the handler - this will log to stderr in test mode
			// In real usage, the RequestLogging middleware sets up the context properly
			server.handleLoginPost(w, req)

			// The test passes if the handler doesn't panic and returns the expected status
			// The actual logging can be observed in the test output
			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, w.Code)
			}
		})
	}
}

// TestCategorizeLoginError tests the error categorization logic
func TestCategorizeLoginError(t *testing.T) {
	server := &Server{}

	tests := []struct {
		name     string
		error    string
		expected UIErrorType
	}{
		{"network timeout", "request timed out - please check your connection", ErrorTypeNetwork},
		{"network connection refused", "connection refused", ErrorTypeNetwork},
		{"network error generic", "network error - please check your connection", ErrorTypeNetwork},
		{"auth failure", "authentication failed: invalid credentials", ErrorTypeAuthentication},
		{"system marshal error", "failed to marshal login request", ErrorTypeSystem},
		{"system decode error", "failed to decode response", ErrorTypeSystem},
		{"unknown error", "some unknown error", ErrorTypeAuthentication},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := &testError{message: tt.error}
			result := server.categorizeLoginError(err)
			if result != tt.expected {
				t.Errorf("Expected %s, got %s for error: %s", tt.expected, result, tt.error)
			}
		})
	}
}

// testError is a simple error implementation for testing
type testError struct {
	message string
}

func (e *testError) Error() string {
	return e.message
}
